<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وضعیت پرداخت</title>
    <style>
        @font-face {
            font-family: 'EstedadRegular';
            src: url('./Estedad-Regular.ttf') format('truetype');   
            font-weight: normal;
            font-style: normal;
        }

        /* ... (rest of the CSS styles are the same) */

        body {
            font-family: 'EstedadRegular', sans-serif;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
        }

        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
            width: 400px;
            transition: transform 0.2s ease-in-out;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        .status-message {
            font-size: 20px;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .success {
            color: #28a745;
        }

        .failure {
            color: #dc3545;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            box-shadow: 2px 2px 5px rgba(0,0,0,0.1);
			font-family: 'EstedadRegular';
        }

        button:hover {
            background-color: #0056b3;
        }

    </style>
    <script>
        function getStatusFromUrl() {
            const params = new URLSearchParams(window.location.search);
            return params.get("status") === "Success";
        }

        function redirectToApp() {
            let screen = "";
            if (getStatusFromUrl()) {
                screen = "profile";
            } else {
                screen = "subscription";
            }
            window.location.href = "fa.speakup.ai://" + screen;
        }

        window.onload = function () {
            const statusElement = document.getElementById("payment-status");
            const isSuccess = getStatusFromUrl();
            statusElement.innerText = `پرداخت شما ${isSuccess ? "موفق" : "ناموفق"} بود.`;
            statusElement.classList.add(isSuccess ? "success" : "failure");

            // Automatically redirect after 5 seconds
            setTimeout(redirectToApp, 5000); // 5000 milliseconds = 5 seconds
        };
    </script>
</head>
<body>
    <div class="card">
        <h1>وضعیت پرداخت</h1>
        <p id="payment-status" class="status-message">در حال بررسی وضعیت پرداخت...</p>
        <button onclick="redirectToApp()">بازگشت به اپلیکیشن</button>
    </div>
</body>
</html>